rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {
    // Allow authenticated users to read and write their own files
    match /post_images/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Allow authenticated users to read and write their own videos
    match /post_videos/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Allow authenticated users to read and write their own video ads
    match /video_ads/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Allow authenticated users to read and write their own profile images
    match /profile_images/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Allow anyone to read public files (for viewing posts)
    match /{allPaths=**} {
      allow read;
    }
  }
}
